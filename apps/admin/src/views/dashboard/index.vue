<script setup lang="ts">
import {
  Alert,
  AlertDescription,
  Avatar,
  AvatarFallback,
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Progress,
} from '@billing/ui'
import {
  Activity,
  AlertTriangle,
  ArrowDownRight,
  ArrowUpRight,
  BarChart3,
  CheckCircle,
  CreditCard,
  DollarSign,
  Info,
  Key,
  Minus,
  Package,
  PieChart,
  TrendingUp,
  Users,
  Wallet,
  XCircle,
} from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { dashboardApi, type DashboardStats, type DashboardData } from '@/api'

const router = useRouter()

// 统计数据
const stats = ref<DashboardStats>({
  // 核心指标
  total_revenue: 0,
  month_revenue: 0,
  today_revenue: 0,
  total_users: 0,
  active_users: 0,
  total_keys: 0,
  active_keys: 0,
  blocked_keys: 0,

  // 计费相关
  total_quota_packages: 0,
  active_quota_packages: 0,
  total_balance: 0,
  avg_user_balance: 0,

  // 使用量统计
  total_usage: 0,
  month_usage: 0,
  today_usage: 0,
  avg_daily_usage: 0,

  // 趋势数据
  revenue_growth: 0,
  user_growth: 0,
  usage_growth: 0,
  balance_growth: 0,

  // 模块统计
  module_stats: [],
})

// 模块使用统计 - 现在从API获取
const moduleStats = computed(() => stats.value.module_stats || [])

// 近期活跃用户
const recentActiveUsers = ref([
  { id: '1', name: '张三', email: '<EMAIL>', balance: 1250.50, usage: 125000, lastActive: '刚刚' },
  { id: '2', name: '李四', email: '<EMAIL>', balance: 850.25, usage: 89000, lastActive: '5分钟前' },
  { id: '3', name: '王五', email: '<EMAIL>', balance: 2100.00, usage: 156000, lastActive: '10分钟前' },
  { id: '4', name: '赵六', email: '<EMAIL>', balance: 560.80, usage: 67000, lastActive: '15分钟前' },
  { id: '5', name: '钱七', email: '<EMAIL>', balance: 1820.30, usage: 234000, lastActive: '20分钟前' },
])

// 系统健康状态
const systemHealth = ref({
  apiResponseTime: 125, // ms
  errorRate: 0.02, // %
  uptime: 99.9, // %
  queueDepth: 3,
  activeConnections: 1247,
  lastCheck: new Date().toISOString(),
})

// 近期充值记录
const recentRecharges = ref([
  { id: '1', user: '张三', amount: 500.00, type: 'admin', time: '2分钟前' },
  { id: '2', user: '李四', amount: 1000.00, type: 'alipay', time: '8分钟前' },
  { id: '3', user: '王五', amount: 200.00, type: 'wechat', time: '15分钟前' },
  { id: '4', user: '赵六', amount: 800.00, type: 'admin', time: '25分钟前' },
])

// 计算属性：总体趋势
const overallTrend = computed(() => {
  const trends = [stats.value.revenueGrowth, stats.value.userGrowth, stats.value.balanceGrowth]
  const avgGrowth = trends.reduce((sum, trend) => sum + trend, 0) / trends.length
  return {
    direction: avgGrowth > 0 ? 'up' : avgGrowth < 0 ? 'down' : 'stable',
    percentage: Math.abs(avgGrowth).toFixed(1),
    value: avgGrowth,
  }
})

// 格式化货币
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  }).format(amount)
}

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toLocaleString()
}

// 获取趋势图标
function getTrendIcon(growth: number) {
  if (growth > 0)
    return ArrowUpRight
  if (growth < 0)
    return ArrowDownRight
  return Minus
}

// 获取趋势颜色
function getTrendColor(growth: number) {
  if (growth > 0)
    return 'text-green-600'
  if (growth < 0)
    return 'text-red-600'
  return 'text-gray-600'
}

// 获取充值类型标签
function getRechargeTypeLabel(type: string) {
  const types = {
    admin: '管理员',
    alipay: '支付宝',
    wechat: '微信',
    bank: '银行',
  }
  return types[type as keyof typeof types] || type
}

// 获取充值类型颜色
function getRechargeTypeColor(type: string) {
  const colors = {
    admin: 'bg-purple-100 text-purple-800',
    alipay: 'bg-blue-100 text-blue-800',
    wechat: 'bg-green-100 text-green-800',
    bank: 'bg-orange-100 text-orange-800',
  }
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

// 导航到详细页面
function navigateTo(path: string) {
  router.push(path)
}

// 获取健康状态颜色
function getHealthColor(value: number, threshold: { good: number, warning: number }) {
  if (value >= threshold.good)
    return 'text-green-600'
  if (value >= threshold.warning)
    return 'text-yellow-600'
  return 'text-red-600'
}

// 获取健康状态图标
function getHealthIcon(value: number, threshold: { good: number, warning: number }) {
  if (value >= threshold.good)
    return CheckCircle
  if (value >= threshold.warning)
    return AlertTriangle
  return XCircle
}

// 获取统计数据
async function fetchDashboardData() {
  try {
    // 这里应该调用实际的API获取数据
    // const response = await dashboardApi.getStats()
    // stats.value = response

    console.log('Dashboard数据获取成功')
  }
  catch (error) {
    console.error('获取Dashboard数据失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchDashboardData()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          计费系统概览
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          监控系统运行状态、用户活动、收入情况和资源使用情况。
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Badge :variant="overallTrend.direction === 'up' ? 'default' : overallTrend.direction === 'down' ? 'destructive' : 'secondary'">
          <component
            :is="getTrendIcon(overallTrend.value)"
            class="w-3 h-3 mr-1"
          />
          总体趋势 {{ overallTrend.direction === 'up' ? '+' : overallTrend.direction === 'down' ? '-' : '' }}{{ overallTrend.percentage }}%
        </Badge>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- 总收入 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            总收入
          </CardTitle>
          <DollarSign class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatCurrency(stats.totalRevenue) }}
          </div>
          <div class="flex items-center gap-1 text-xs text-muted-foreground">
            <component
              :is="getTrendIcon(stats.revenueGrowth)"
              :class="`w-3 h-3 ${getTrendColor(stats.revenueGrowth)}`"
            />
            <span :class="getTrendColor(stats.revenueGrowth)">{{ stats.revenueGrowth > 0 ? '+' : '' }}{{ stats.revenueGrowth }}%</span>
            <span>与上月对比</span>
          </div>
        </CardContent>
      </Card>

      <!-- 活跃用户 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            活跃用户
          </CardTitle>
          <Users class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatNumber(stats.activeUsers) }}
          </div>
          <div class="flex items-center gap-1 text-xs text-muted-foreground">
            <component
              :is="getTrendIcon(stats.userGrowth)"
              :class="`w-3 h-3 ${getTrendColor(stats.userGrowth)}`"
            />
            <span :class="getTrendColor(stats.userGrowth)">{{ stats.userGrowth > 0 ? '+' : '' }}{{ stats.userGrowth }}%</span>
            <span>用户增长</span>
          </div>
        </CardContent>
      </Card>

      <!-- API Keys -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            API Keys
          </CardTitle>
          <Key class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatNumber(stats.activeKeys) }}
          </div>
          <div class="text-xs text-muted-foreground">
            总计 {{ formatNumber(stats.totalKeys) }}，阻止 {{ formatNumber(stats.blockedKeys) }}
          </div>
        </CardContent>
      </Card>

      <!-- 用户余额 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            用户余额
          </CardTitle>
          <Wallet class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ formatCurrency(stats.totalBalance) }}
          </div>
          <div class="flex items-center gap-1 text-xs text-muted-foreground">
            <component
              :is="getTrendIcon(stats.balanceGrowth)"
              :class="`w-3 h-3 ${getTrendColor(stats.balanceGrowth)}`"
            />
            <span :class="getTrendColor(stats.balanceGrowth)">{{ stats.balanceGrowth > 0 ? '+' : '' }}{{ stats.balanceGrowth }}%</span>
            <span>余额增长</span>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 系统健康状态 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Activity class="w-5 h-5" />
          系统健康状态
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div class="text-center">
            <div class="flex items-center justify-center mb-2">
              <component
                :is="getHealthIcon(systemHealth.uptime, { good: 99.5, warning: 99.0 })"
                :class="`w-5 h-5 ${getHealthColor(systemHealth.uptime, { good: 99.5, warning: 99.0 })}`"
              />
            </div>
            <div
              class="text-2xl font-bold"
              :class="getHealthColor(systemHealth.uptime, { good: 99.5, warning: 99.0 })"
            >
              {{ systemHealth.uptime }}%
            </div>
            <div class="text-xs text-gray-500">
              系统可用性
            </div>
          </div>

          <div class="text-center">
            <div class="flex items-center justify-center mb-2">
              <component
                :is="getHealthIcon(200 - systemHealth.apiResponseTime, { good: 150, warning: 100 })"
                :class="`w-5 h-5 ${getHealthColor(200 - systemHealth.apiResponseTime, { good: 150, warning: 100 })}`"
              />
            </div>
            <div
              class="text-2xl font-bold"
              :class="getHealthColor(200 - systemHealth.apiResponseTime, { good: 150, warning: 100 })"
            >
              {{ systemHealth.apiResponseTime }}ms
            </div>
            <div class="text-xs text-gray-500">
              响应时间
            </div>
          </div>

          <div class="text-center">
            <div class="flex items-center justify-center mb-2">
              <component
                :is="getHealthIcon(100 - (systemHealth.errorRate * 1000), { good: 99, warning: 95 })"
                :class="`w-5 h-5 ${getHealthColor(100 - (systemHealth.errorRate * 1000), { good: 99, warning: 95 })}`"
              />
            </div>
            <div
              class="text-2xl font-bold"
              :class="getHealthColor(100 - (systemHealth.errorRate * 1000), { good: 99, warning: 95 })"
            >
              {{ (systemHealth.errorRate * 100).toFixed(2) }}%
            </div>
            <div class="text-xs text-gray-500">
              错误率
            </div>
          </div>

          <div class="text-center">
            <div class="flex items-center justify-center mb-2">
              <Activity class="w-5 h-5 text-blue-600" />
            </div>
            <div class="text-2xl font-bold text-blue-600">
              {{ formatNumber(systemHealth.activeConnections) }}
            </div>
            <div class="text-xs text-gray-500">
              活跃连接
            </div>
          </div>

          <div class="text-center">
            <div class="flex items-center justify-center mb-2">
              <BarChart3 class="w-5 h-5 text-purple-600" />
            </div>
            <div class="text-2xl font-bold text-purple-600">
              {{ systemHealth.queueDepth }}
            </div>
            <div class="text-xs text-gray-500">
              队列深度
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：服务使用统计 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 按服务类型统计 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <PieChart class="w-5 h-5" />
                服务使用统计
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                @click="navigateTo('/billing/keys')"
              >
                查看详情
                <ArrowUpRight class="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="module in moduleStats"
                :key="module.module"
                class="space-y-3"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <div :class="`w-3 h-3 rounded-full ${module.color}`" />
                    <div>
                      <div class="font-medium">
                        {{ module.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ module.keys }} 个Key使用
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">
                      {{ formatCurrency(module.revenue) }}
                    </div>
                    <div class="flex items-center gap-1 text-xs">
                      <component
                        :is="getTrendIcon(module.growth)"
                        :class="`w-3 h-3 ${getTrendColor(module.growth)}`"
                      />
                      <span :class="getTrendColor(module.growth)">{{ module.growth > 0 ? '+' : '' }}{{ module.growth }}%</span>
                    </div>
                  </div>
                </div>
                <div class="space-y-1">
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>使用量: {{ formatNumber(module.usage) }}</span>
                    <span>{{ Math.round((module.usage / stats.totalUsage) * 100) }}%</span>
                  </div>
                  <Progress
                    :value="(module.usage / stats.totalUsage) * 100"
                    class="h-2"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 今日收入分析 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <TrendingUp class="w-5 h-5" />
                今日收入分析
              </CardTitle>
              <Badge variant="outline">
                {{ formatCurrency(stats.todayRevenue) }}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-3 gap-4">
              <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-lg font-bold text-green-600">
                  {{ formatCurrency(stats.todayRevenue * 0.6) }}
                </div>
                <div class="text-xs text-green-600">
                  LLM服务
                </div>
              </div>
              <div class="text-center p-3 bg-blue-50 rounded-lg">
                <div class="text-lg font-bold text-blue-600">
                  {{ formatCurrency(stats.todayRevenue * 0.25) }}
                </div>
                <div class="text-xs text-blue-600">
                  TTS服务
                </div>
              </div>
              <div class="text-center p-3 bg-purple-50 rounded-lg">
                <div class="text-lg font-bold text-purple-600">
                  {{ formatCurrency(stats.todayRevenue * 0.15) }}
                </div>
                <div class="text-xs text-purple-600">
                  ASR服务
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：实时活动 -->
      <div class="space-y-6">
        <!-- 近期活跃用户 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <Users class="w-5 h-5" />
                近期活跃用户
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                @click="navigateTo('/users')"
              >
                查看全部
                <ArrowUpRight class="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div
                v-for="user in recentActiveUsers"
                :key="user.id"
                class="flex items-center justify-between"
              >
                <div class="flex items-center gap-3">
                  <Avatar class="h-8 w-8">
                    <AvatarFallback>{{ user.name.charAt(0) }}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div class="text-sm font-medium">
                      {{ user.name }}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ user.lastActive }}
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-xs font-medium">
                    {{ formatCurrency(user.balance) }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ formatNumber(user.usage) }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 近期充值记录 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="flex items-center gap-2">
                <CreditCard class="w-5 h-5" />
                近期充值
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                @click="navigateTo('/billing/recharge')"
              >
                查看全部
                <ArrowUpRight class="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div
                v-for="recharge in recentRecharges"
                :key="recharge.id"
                class="flex items-center justify-between"
              >
                <div>
                  <div class="text-sm font-medium">
                    {{ recharge.user }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ recharge.time }}
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-green-600">
                    +{{ formatCurrency(recharge.amount) }}
                  </div>
                  <div :class="`text-xs px-2 py-1 rounded-full ${getRechargeTypeColor(recharge.type)}`">
                    {{ getRechargeTypeLabel(recharge.type) }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 系统公告 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Info class="w-5 h-5" />
              系统公告
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <Alert>
                <AlertTriangle class="h-4 w-4" />
                <AlertDescription>
                  <strong>维护通知：</strong>系统将于今晚23:00-01:00进行例行维护，期间可能影响服务。
                </AlertDescription>
              </Alert>

              <Alert>
                <Info class="h-4 w-4" />
                <AlertDescription>
                  <strong>功能更新：</strong>新增了批量配额管理功能，支持批量为用户分配资源包。
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 快速操作 -->
    <Card>
      <CardHeader>
        <CardTitle>快速操作</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo('/billing/keys')"
          >
            <Key class="w-6 h-6 mb-2" />
            <span>管理API Key</span>
          </Button>

          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo('/billing/quota')"
          >
            <Package class="w-6 h-6 mb-2" />
            <span>资源包管理</span>
          </Button>

          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo('/billing/recharge')"
          >
            <CreditCard class="w-6 h-6 mb-2" />
            <span>充值管理</span>
          </Button>

          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="navigateTo('/users')"
          >
            <Users class="w-6 h-6 mb-2" />
            <span>用户管理</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
