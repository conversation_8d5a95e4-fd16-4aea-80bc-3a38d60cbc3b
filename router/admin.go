package router

import (
	"git.uozi.org/uozi/potato-billing-api/api/admin/settings"
	"git.uozi.org/uozi/potato-billing-api/api/admin/user"
	"git.uozi.org/uozi/potato-billing-api/api/billing"
	"github.com/uozi-tech/cosy"
)

func initAdminRouter() {
	r := cosy.GetEngine()
	admin := r.Group("/admin/", AuthRequired())
	{
		user.InitUserRouter(admin)
		settings.InitRouter(admin)
		billing.RegisterRoutes(admin)

		// Dashboard路由 - 暂时注释，需要在admin包中实现
		// admin.GET("/dashboard/stats", adminapi.GetDashboardStats)
		// admin.GET("/dashboard/data", adminapi.GetDashboardData)
	}
}
